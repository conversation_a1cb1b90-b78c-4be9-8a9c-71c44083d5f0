#!/usr/bin/env python3
"""
Advanced Strategy Engine for AI Trading System
Multiple sophisticated trading strategies with backtesting and optimization
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timezone
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from abc import ABC, abstractmethod
import json

logger = logging.getLogger("AdvancedStrategyEngine")


@dataclass
class Signal:
    """Trading signal data structure."""
    symbol: str
    direction: str  # BUY, SELL, HOLD
    confidence: float
    entry_price: float
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    position_size: float = 0.1
    strategy_name: str = ""
    timestamp: str = ""
    metadata: Dict[str, Any] = None


@dataclass
class Trade:
    """Trade execution data structure."""
    signal: Signal
    entry_time: str
    entry_price: float
    exit_time: Optional[str] = None
    exit_price: Optional[float] = None
    quantity: float = 0.0
    pnl: float = 0.0
    status: str = "OPEN"  # OPEN, CLOSED, CANCELLED


class BaseStrategy(ABC):
    """Base class for all trading strategies."""
    
    def __init__(self, name: str, parameters: Dict[str, Any]):
        self.name = name
        self.parameters = parameters
        self.performance_metrics = {}
        self.signals_generated = []
        
    @abstractmethod
    def generate_signal(self, market_data: Dict[str, Any]) -> Optional[Signal]:
        """Generate trading signal based on market data."""
        pass
    
    @abstractmethod
    def update_parameters(self, new_parameters: Dict[str, Any]):
        """Update strategy parameters."""
        pass
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get strategy performance metrics."""
        return self.performance_metrics


class MomentumStrategy(BaseStrategy):
    """Momentum-based trading strategy."""
    
    def __init__(self, parameters: Dict[str, Any] = None):
        default_params = {
            "lookback_period": 20,
            "momentum_threshold": 0.005,  # Lower threshold for more signals
            "rsi_overbought": 65,         # Lower threshold for more signals
            "rsi_oversold": 35,           # Higher threshold for more signals
            "volume_threshold": 0.8       # Lower threshold for more signals
        }
        params = {**default_params, **(parameters or {})}
        super().__init__("Momentum Strategy", params)
    
    def generate_signal(self, market_data: Dict[str, Any]) -> Optional[Signal]:
        """Generate momentum-based signal."""
        try:
            price_history = market_data.get('price_history', [])
            current_price = market_data.get('price', 0)
            rsi = market_data.get('rsi', 50)
            volume_ratio = market_data.get('volume_ratio', 1.0)
            
            if len(price_history) < self.parameters["lookback_period"]:
                return None
            
            # Calculate momentum
            lookback = self.parameters["lookback_period"]
            momentum = (current_price - price_history[-lookback]) / price_history[-lookback]
            
            # Generate signal based on momentum and RSI
            signal_direction = "HOLD"
            confidence = 0.5
            
            if (momentum > self.parameters["momentum_threshold"] and 
                rsi < self.parameters["rsi_overbought"] and
                volume_ratio > self.parameters["volume_threshold"]):
                signal_direction = "BUY"
                confidence = min(0.9, 0.5 + abs(momentum) * 10)
                
            elif (momentum < -self.parameters["momentum_threshold"] and 
                  rsi > self.parameters["rsi_oversold"] and
                  volume_ratio > self.parameters["volume_threshold"]):
                signal_direction = "SELL"
                confidence = min(0.9, 0.5 + abs(momentum) * 10)
            
            if signal_direction != "HOLD":
                signal = Signal(
                    symbol=market_data.get('symbol', 'UNKNOWN'),
                    direction=signal_direction,
                    confidence=confidence,
                    entry_price=current_price,
                    stop_loss=current_price * (0.98 if signal_direction == "BUY" else 1.02),
                    take_profit=current_price * (1.04 if signal_direction == "BUY" else 0.96),
                    strategy_name=self.name,
                    timestamp=datetime.now(timezone.utc).isoformat(),
                    metadata={"momentum": momentum, "rsi": rsi, "volume_ratio": volume_ratio}
                )
                
                self.signals_generated.append(signal)
                return signal
            
            return None
            
        except Exception as e:
            logger.error(f"Momentum strategy error: {e}")
            return None
    
    def update_parameters(self, new_parameters: Dict[str, Any]):
        """Update strategy parameters."""
        self.parameters.update(new_parameters)


class MeanReversionStrategy(BaseStrategy):
    """Mean reversion trading strategy."""
    
    def __init__(self, parameters: Dict[str, Any] = None):
        default_params = {
            "lookback_period": 20,
            "std_dev_threshold": 1.5,     # Lower threshold for more signals
            "rsi_extreme_high": 70,       # Lower threshold for more signals
            "rsi_extreme_low": 30,        # Higher threshold for more signals
            "bollinger_position_threshold": 0.7  # Lower threshold for more signals
        }
        params = {**default_params, **(parameters or {})}
        super().__init__("Mean Reversion Strategy", params)
    
    def generate_signal(self, market_data: Dict[str, Any]) -> Optional[Signal]:
        """Generate mean reversion signal."""
        try:
            price_history = market_data.get('price_history', [])
            current_price = market_data.get('price', 0)
            rsi = market_data.get('rsi', 50)
            
            if len(price_history) < self.parameters["lookback_period"]:
                return None
            
            # Calculate Bollinger Bands
            lookback = self.parameters["lookback_period"]
            prices = np.array(price_history[-lookback:])
            mean_price = np.mean(prices)
            std_price = np.std(prices)
            
            upper_band = mean_price + (self.parameters["std_dev_threshold"] * std_price)
            lower_band = mean_price - (self.parameters["std_dev_threshold"] * std_price)
            
            # Calculate position within bands
            bb_position = (current_price - lower_band) / (upper_band - lower_band)
            
            signal_direction = "HOLD"
            confidence = 0.5
            
            # Oversold condition (buy signal)
            if (bb_position < (1 - self.parameters["bollinger_position_threshold"]) and 
                rsi < self.parameters["rsi_extreme_low"]):
                signal_direction = "BUY"
                confidence = min(0.9, 0.6 + (1 - bb_position))
                
            # Overbought condition (sell signal)
            elif (bb_position > self.parameters["bollinger_position_threshold"] and 
                  rsi > self.parameters["rsi_extreme_high"]):
                signal_direction = "SELL"
                confidence = min(0.9, 0.6 + bb_position)
            
            if signal_direction != "HOLD":
                signal = Signal(
                    symbol=market_data.get('symbol', 'UNKNOWN'),
                    direction=signal_direction,
                    confidence=confidence,
                    entry_price=current_price,
                    stop_loss=upper_band if signal_direction == "BUY" else lower_band,
                    take_profit=mean_price,
                    strategy_name=self.name,
                    timestamp=datetime.now(timezone.utc).isoformat(),
                    metadata={"bb_position": bb_position, "rsi": rsi, "mean_price": mean_price}
                )
                
                self.signals_generated.append(signal)
                return signal
            
            return None
            
        except Exception as e:
            logger.error(f"Mean reversion strategy error: {e}")
            return None
    
    def update_parameters(self, new_parameters: Dict[str, Any]):
        """Update strategy parameters."""
        self.parameters.update(new_parameters)


class BreakoutStrategy(BaseStrategy):
    """Breakout trading strategy."""
    
    def __init__(self, parameters: Dict[str, Any] = None):
        default_params = {
            "lookback_period": 20,
            "breakout_threshold": 0.01,   # Lower threshold for more signals
            "volume_confirmation": 1.2,   # Lower threshold for more signals
            "atr_multiplier": 1.5
        }
        params = {**default_params, **(parameters or {})}
        super().__init__("Breakout Strategy", params)
    
    def generate_signal(self, market_data: Dict[str, Any]) -> Optional[Signal]:
        """Generate breakout signal."""
        try:
            price_history = market_data.get('price_history', [])
            current_price = market_data.get('price', 0)
            volume_ratio = market_data.get('volume_ratio', 1.0)
            
            if len(price_history) < self.parameters["lookback_period"]:
                return None
            
            # Calculate support and resistance levels
            lookback = self.parameters["lookback_period"]
            prices = np.array(price_history[-lookback:])
            resistance = np.max(prices)
            support = np.min(prices)
            
            # Calculate ATR for stop loss
            atr = np.std(prices) * self.parameters["atr_multiplier"]
            
            signal_direction = "HOLD"
            confidence = 0.5
            
            # Upward breakout
            breakout_up = (current_price - resistance) / resistance
            if (breakout_up > self.parameters["breakout_threshold"] and 
                volume_ratio > self.parameters["volume_confirmation"]):
                signal_direction = "BUY"
                confidence = min(0.9, 0.6 + breakout_up * 10)
                
            # Downward breakout
            breakout_down = (support - current_price) / support
            if (breakout_down > self.parameters["breakout_threshold"] and 
                volume_ratio > self.parameters["volume_confirmation"]):
                signal_direction = "SELL"
                confidence = min(0.9, 0.6 + breakout_down * 10)
            
            if signal_direction != "HOLD":
                signal = Signal(
                    symbol=market_data.get('symbol', 'UNKNOWN'),
                    direction=signal_direction,
                    confidence=confidence,
                    entry_price=current_price,
                    stop_loss=current_price - atr if signal_direction == "BUY" else current_price + atr,
                    take_profit=current_price + (2 * atr) if signal_direction == "BUY" else current_price - (2 * atr),
                    strategy_name=self.name,
                    timestamp=datetime.now(timezone.utc).isoformat(),
                    metadata={"resistance": resistance, "support": support, "atr": atr}
                )
                
                self.signals_generated.append(signal)
                return signal
            
            return None
            
        except Exception as e:
            logger.error(f"Breakout strategy error: {e}")
            return None
    
    def update_parameters(self, new_parameters: Dict[str, Any]):
        """Update strategy parameters."""
        self.parameters.update(new_parameters)


class AdvancedStrategyEngine:
    """Advanced strategy engine managing multiple strategies."""
    
    def __init__(self):
        self.strategies = {}
        self.active_trades = []
        self.closed_trades = []
        self.performance_metrics = {}
        
        # Initialize default strategies
        self._initialize_strategies()
    
    def _initialize_strategies(self):
        """Initialize default trading strategies."""
        self.strategies = {
            "momentum": MomentumStrategy(),
            "mean_reversion": MeanReversionStrategy(),
            "breakout": BreakoutStrategy()
        }
        
        logger.info(f"✅ Initialized {len(self.strategies)} trading strategies")
    
    def add_strategy(self, strategy_id: str, strategy: BaseStrategy):
        """Add a new strategy to the engine."""
        self.strategies[strategy_id] = strategy
        logger.info(f"✅ Added strategy: {strategy_id}")
    
    def remove_strategy(self, strategy_id: str):
        """Remove a strategy from the engine."""
        if strategy_id in self.strategies:
            del self.strategies[strategy_id]
            logger.info(f"🗑️ Removed strategy: {strategy_id}")
    
    def generate_signals(self, market_data: Dict[str, Any]) -> List[Signal]:
        """Generate signals from all active strategies."""
        signals = []
        
        for strategy_id, strategy in self.strategies.items():
            try:
                signal = strategy.generate_signal(market_data)
                if signal:
                    signals.append(signal)
                    logger.info(f"📊 {strategy_id} generated {signal.direction} signal (confidence: {signal.confidence:.2f})")
            except Exception as e:
                logger.error(f"Strategy {strategy_id} error: {e}")
        
        return signals
    
    def backtest_strategy(self, strategy_id: str, historical_data: List[Dict[str, Any]], 
                         initial_capital: float = 10000) -> Dict[str, Any]:
        """Backtest a specific strategy."""
        if strategy_id not in self.strategies:
            return {"error": f"Strategy {strategy_id} not found"}
        
        strategy = self.strategies[strategy_id]
        trades = []
        capital = initial_capital
        equity_curve = [capital]
        
        try:
            for i, data_point in enumerate(historical_data):
                # Generate signal
                signal = strategy.generate_signal(data_point)
                
                if signal and signal.direction != "HOLD":
                    # Simulate trade execution
                    position_value = capital * signal.position_size
                    
                    # Simple exit after 5 periods or at stop/target
                    exit_index = min(i + 5, len(historical_data) - 1)
                    exit_price = historical_data[exit_index].get('price', signal.entry_price)
                    
                    # Calculate P&L
                    if signal.direction == "BUY":
                        pnl = (exit_price - signal.entry_price) / signal.entry_price * position_value
                    else:  # SELL
                        pnl = (signal.entry_price - exit_price) / signal.entry_price * position_value
                    
                    capital += pnl
                    equity_curve.append(capital)
                    
                    trade = Trade(
                        signal=signal,
                        entry_time=data_point.get('timestamp', ''),
                        entry_price=signal.entry_price,
                        exit_time=historical_data[exit_index].get('timestamp', ''),
                        exit_price=exit_price,
                        quantity=position_value / signal.entry_price,
                        pnl=pnl,
                        status="CLOSED"
                    )
                    trades.append(trade)
            
            # Calculate performance metrics
            total_return = (capital - initial_capital) / initial_capital
            winning_trades = [t for t in trades if t.pnl > 0]
            losing_trades = [t for t in trades if t.pnl < 0]
            
            win_rate = len(winning_trades) / len(trades) if trades else 0
            avg_win = np.mean([t.pnl for t in winning_trades]) if winning_trades else 0
            avg_loss = np.mean([t.pnl for t in losing_trades]) if losing_trades else 0
            profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')
            
            # Calculate Sharpe ratio (simplified)
            returns = np.diff(equity_curve) / equity_curve[:-1]
            sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
            
            # Calculate maximum drawdown
            peak = np.maximum.accumulate(equity_curve)
            drawdown = (equity_curve - peak) / peak
            max_drawdown = np.min(drawdown)
            
            backtest_results = {
                "strategy_id": strategy_id,
                "initial_capital": initial_capital,
                "final_capital": capital,
                "total_return": round(total_return, 4),
                "total_trades": len(trades),
                "winning_trades": len(winning_trades),
                "losing_trades": len(losing_trades),
                "win_rate": round(win_rate, 4),
                "profit_factor": round(profit_factor, 2),
                "sharpe_ratio": round(sharpe_ratio, 2),
                "max_drawdown": round(abs(max_drawdown), 4),
                "avg_win": round(avg_win, 2),
                "avg_loss": round(avg_loss, 2),
                "equity_curve": equity_curve[-10:],  # Last 10 points
                "sample_trades": trades[-5:] if len(trades) >= 5 else trades  # Last 5 trades
            }
            
            # Store performance metrics
            strategy.performance_metrics = backtest_results
            
            logger.info(f"📈 Backtest completed for {strategy_id}: {total_return:.2%} return, {win_rate:.1%} win rate")
            
            return backtest_results
            
        except Exception as e:
            logger.error(f"Backtesting error for {strategy_id}: {e}")
            return {"error": str(e)}
    
    def optimize_strategy(self, strategy_id: str, historical_data: List[Dict[str, Any]], 
                         parameter_ranges: Dict[str, List]) -> Dict[str, Any]:
        """Optimize strategy parameters using grid search."""
        if strategy_id not in self.strategies:
            return {"error": f"Strategy {strategy_id} not found"}
        
        best_params = None
        best_performance = -float('inf')
        optimization_results = []
        
        try:
            # Generate parameter combinations (simplified grid search)
            param_combinations = self._generate_parameter_combinations(parameter_ranges)
            
            for params in param_combinations[:10]:  # Limit to 10 combinations for demo
                # Update strategy parameters
                original_params = self.strategies[strategy_id].parameters.copy()
                self.strategies[strategy_id].update_parameters(params)
                
                # Run backtest
                backtest_result = self.backtest_strategy(strategy_id, historical_data)
                
                if "error" not in backtest_result:
                    performance_score = backtest_result.get("sharpe_ratio", 0)
                    optimization_results.append({
                        "parameters": params,
                        "performance": performance_score,
                        "total_return": backtest_result.get("total_return", 0),
                        "win_rate": backtest_result.get("win_rate", 0)
                    })
                    
                    if performance_score > best_performance:
                        best_performance = performance_score
                        best_params = params.copy()
                
                # Restore original parameters
                self.strategies[strategy_id].update_parameters(original_params)
            
            # Apply best parameters
            if best_params:
                self.strategies[strategy_id].update_parameters(best_params)
                logger.info(f"🎯 Optimized {strategy_id}: Sharpe ratio {best_performance:.2f}")
            
            return {
                "strategy_id": strategy_id,
                "best_parameters": best_params,
                "best_performance": best_performance,
                "optimization_results": optimization_results[:5],  # Top 5 results
                "total_combinations_tested": len(param_combinations)
            }
            
        except Exception as e:
            logger.error(f"Optimization error for {strategy_id}: {e}")
            return {"error": str(e)}
    
    def _generate_parameter_combinations(self, parameter_ranges: Dict[str, List]) -> List[Dict[str, Any]]:
        """Generate parameter combinations for optimization."""
        combinations = []
        
        # Simple grid search implementation
        param_names = list(parameter_ranges.keys())
        if not param_names:
            return [{}]
        
        # Generate combinations (simplified - just take first few values)
        for i in range(min(10, len(parameter_ranges[param_names[0]]))):
            combination = {}
            for param_name in param_names:
                values = parameter_ranges[param_name]
                combination[param_name] = values[i % len(values)]
            combinations.append(combination)
        
        return combinations
    
    def get_strategy_performance(self) -> Dict[str, Any]:
        """Get performance summary for all strategies."""
        performance_summary = {}
        
        for strategy_id, strategy in self.strategies.items():
            performance_summary[strategy_id] = {
                "signals_generated": len(strategy.signals_generated),
                "performance_metrics": strategy.performance_metrics,
                "parameters": strategy.parameters
            }
        
        return performance_summary
    
    def get_engine_status(self) -> Dict[str, Any]:
        """Get comprehensive engine status."""
        return {
            "strategies_loaded": len(self.strategies),
            "strategy_names": list(self.strategies.keys()),
            "active_trades": len(self.active_trades),
            "closed_trades": len(self.closed_trades),
            "total_signals_generated": sum(len(s.signals_generated) for s in self.strategies.values()),
            "performance_summary": self.get_strategy_performance()
        }


# Test the Strategy Engine
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    print("🔄 Testing Advanced Strategy Engine")
    print("=" * 60)
    
    # Initialize engine
    engine = AdvancedStrategyEngine()
    
    # Generate test market data
    np.random.seed(42)
    historical_data = []
    base_price = 45000
    
    for i in range(100):
        price = base_price * (1 + np.random.normal(0, 0.02))
        data_point = {
            'price': price,
            'price_history': [base_price * (1 + np.random.normal(0, 0.02)) for _ in range(50)],
            'rsi': 50 + 30 * np.sin(i / 10),
            'volume_ratio': 1 + 0.5 * np.random.random(),
            'symbol': 'BTCUSDT',
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        historical_data.append(data_point)
        base_price = price
    
    # Test signal generation
    print("📊 Testing signal generation...")
    signals = engine.generate_signals(historical_data[-1])
    print(f"Generated {len(signals)} signals")
    
    # Test backtesting
    print("\n📈 Testing backtesting...")
    for strategy_id in engine.strategies.keys():
        results = engine.backtest_strategy(strategy_id, historical_data)
        if "error" not in results:
            print(f"{strategy_id}: {results['total_return']:.2%} return, {results['win_rate']:.1%} win rate")
    
    # Test optimization
    print("\n🎯 Testing optimization...")
    param_ranges = {
        "lookback_period": [10, 15, 20, 25],
        "momentum_threshold": [0.01, 0.02, 0.03]
    }
    opt_results = engine.optimize_strategy("momentum", historical_data, param_ranges)
    if "error" not in opt_results:
        print(f"Best parameters: {opt_results['best_parameters']}")
    
    # Get status
    print("\n📋 Engine Status:")
    status = engine.get_engine_status()
    print(f"Strategies: {status['strategies_loaded']}")
    print(f"Total signals: {status['total_signals_generated']}")
    
    print("\n✅ Strategy Engine test completed!")
