
def system_health_check():
    """Continuous system health monitoring"""
    import psutil
    import sqlite3
    from datetime import datetime

    # Check system resources
    memory = psutil.virtual_memory()
    cpu = psutil.cpu_percent(interval=1)
    disk = psutil.disk_usage('.')

    # Store metrics
    conn = sqlite3.connect('enhanced_monitoring.db')
    conn.execute("""
        INSERT INTO system_metrics (timestamp, memory_used_percent, cpu_percent, disk_free_gb, active_processes)
        VALUES (?, ?, ?, ?, ?)
    """, (datetime.now().isoformat(), memory.percent, cpu, disk.free / (1024**3), len(psutil.pids())))
    conn.commit()
    conn.close()

    return {
        'memory_percent': memory.percent,
        'cpu_percent': cpu,
        'disk_free_gb': disk.free / (1024**3),
        'status': 'healthy' if memory.percent < 80 and cpu < 80 else 'warning'
    }
