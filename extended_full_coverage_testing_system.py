#!/usr/bin/env python3
"""
🚀 EXTENDED FULL COVERAGE TESTING INTEGRATION SYSTEM
Complete testing coverage for everything - all models, all systems, all integrations
"""

import asyncio
import aiohttp
import sqlite3
import pandas as pd
import numpy as np
import json
import requests
import subprocess
import time
import threading
import concurrent.futures
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
import random
import traceback
import psutil
import os

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("ExtendedFullCoverageTestingSystem")

@dataclass
class TestResult:
    """Test result with comprehensive data"""
    test_name: str
    status: str  # PASS, FAIL, SKIP, ERROR
    duration_ms: float
    details: Dict[str, Any] = field(default_factory=dict)
    error_message: str = ""
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())

@dataclass
class SystemComponent:
    """System component for testing"""
    name: str
    type: str  # MODEL, SYSTEM, INTEGRATION, DATABASE, API
    status: str = "UNKNOWN"
    tests_run: int = 0
    tests_passed: int = 0
    performance_score: float = 0.0
    last_tested: str = ""

class ExtendedFullCoverageTestingSystem:
    """Extended full coverage testing system for everything"""
    
    def __init__(self):
        self.db_path = "extended_full_coverage_testing.db"
        self.ollama_url = "http://localhost:11434"
        self.session = None
        
        # YOUR EXACT 13 MODELS FOR TESTING
        self.test_models = [
            {'name': 'phi4-reasoning:plus', 'size_gb': 11.0, 'family': 'phi4', 'priority': 'HIGH'},
            {'name': 'nemotron-mini:4b', 'size_gb': 2.7, 'family': 'nemotron', 'priority': 'HIGH'},
            {'name': 'hermes3:8b', 'size_gb': 4.7, 'family': 'hermes', 'priority': 'MEDIUM'},
            {'name': 'marco-o1:7b', 'size_gb': 4.7, 'family': 'marco', 'priority': 'HIGH'},
            {'name': 'magistral:24b', 'size_gb': 14.0, 'family': 'magistral', 'priority': 'MEDIUM'},
            {'name': 'command-r:35b', 'size_gb': 18.0, 'family': 'command', 'priority': 'HIGH'},
            {'name': 'cogito:32b', 'size_gb': 19.0, 'family': 'cogito', 'priority': 'MEDIUM'},
            {'name': 'gemma3:27b', 'size_gb': 17.0, 'family': 'gemma', 'priority': 'MEDIUM'},
            {'name': 'mistral-small:24b', 'size_gb': 14.0, 'family': 'mistral', 'priority': 'MEDIUM'},
            {'name': 'falcon3:10b', 'size_gb': 6.3, 'family': 'falcon', 'priority': 'LOW'},
            {'name': 'granite3.3:8b', 'size_gb': 4.9, 'family': 'granite', 'priority': 'LOW'},
            {'name': 'qwen3:32b', 'size_gb': 20.0, 'family': 'qwen', 'priority': 'HIGH'},
            {'name': 'deepseek-r1:latest', 'size_gb': 5.2, 'family': 'deepseek', 'priority': 'HIGH'}
        ]
        
        # SYSTEM COMPONENTS TO TEST
        self.system_components = {
            'ollama_server': SystemComponent('Ollama Server', 'API'),
            'database': SystemComponent('SQLite Database', 'DATABASE'),
            'market_simulation': SystemComponent('Market Simulation', 'SYSTEM'),
            'trading_engine': SystemComponent('Trading Engine', 'SYSTEM'),
            'portfolio_manager': SystemComponent('Portfolio Manager', 'SYSTEM'),
            'ai_decision_engine': SystemComponent('AI Decision Engine', 'INTEGRATION'),
            'performance_tracker': SystemComponent('Performance Tracker', 'SYSTEM'),
            'risk_manager': SystemComponent('Risk Manager', 'SYSTEM')
        }
        
        # TEST CATEGORIES
        self.test_categories = [
            'model_availability',
            'model_response',
            'trading_decisions',
            'trade_execution',
            'portfolio_management',
            'market_simulation',
            'database_operations',
            'performance_tracking',
            'risk_management',
            'integration_testing',
            'stress_testing',
            'concurrent_testing',
            'error_handling',
            'recovery_testing'
        ]
        
        # TEST RESULTS STORAGE
        self.test_results = {}
        self.component_status = {}
        self.performance_metrics = {}
        
        # TESTING STATE
        self.testing_active = False
        self.test_start_time = None
        self.total_tests_run = 0
        self.total_tests_passed = 0
        
        self._initialize_testing_database()

    def _initialize_testing_database(self):
        """Initialize comprehensive testing database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Test results table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                test_name TEXT,
                test_category TEXT,
                component_name TEXT,
                status TEXT,
                duration_ms REAL,
                details TEXT,
                error_message TEXT,
                timestamp TEXT,
                test_session_id TEXT
            )
        ''')
        
        # Component status table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS component_status (
                component_name TEXT PRIMARY KEY,
                component_type TEXT,
                status TEXT,
                tests_run INTEGER,
                tests_passed INTEGER,
                performance_score REAL,
                last_tested TEXT,
                last_error TEXT
            )
        ''')
        
        # Model testing table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS model_testing (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                model_name TEXT,
                model_family TEXT,
                model_size_gb REAL,
                test_type TEXT,
                response_time_ms REAL,
                success BOOLEAN,
                confidence_score REAL,
                reasoning_quality REAL,
                error_details TEXT,
                timestamp TEXT
            )
        ''')
        
        # Integration testing table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS integration_testing (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                integration_name TEXT,
                components_involved TEXT,
                test_scenario TEXT,
                success BOOLEAN,
                performance_score REAL,
                error_details TEXT,
                timestamp TEXT
            )
        ''')
        
        # Performance benchmarks table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS performance_benchmarks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                benchmark_name TEXT,
                component_name TEXT,
                metric_name TEXT,
                metric_value REAL,
                benchmark_target REAL,
                passed BOOLEAN,
                timestamp TEXT
            )
        ''')
        
        # System health monitoring
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_health (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                cpu_usage REAL,
                memory_usage REAL,
                disk_usage REAL,
                network_status TEXT,
                ollama_status TEXT,
                active_models INTEGER,
                system_load REAL
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info(f"✅ Extended testing database initialized: {self.db_path}")

    async def test_ollama_server_connectivity(self) -> TestResult:
        """Test Ollama server connectivity and status"""
        start_time = time.time()
        
        try:
            # Test basic connectivity
            url = f"{self.ollama_url}/api/tags"
            async with self.session.get(url, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    models_available = len(data.get('models', []))
                    
                    duration_ms = (time.time() - start_time) * 1000
                    
                    return TestResult(
                        test_name="ollama_server_connectivity",
                        status="PASS",
                        duration_ms=duration_ms,
                        details={
                            'server_url': self.ollama_url,
                            'models_available': models_available,
                            'response_status': response.status
                        }
                    )
                else:
                    duration_ms = (time.time() - start_time) * 1000
                    return TestResult(
                        test_name="ollama_server_connectivity",
                        status="FAIL",
                        duration_ms=duration_ms,
                        error_message=f"HTTP {response.status}"
                    )
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            return TestResult(
                test_name="ollama_server_connectivity",
                status="ERROR",
                duration_ms=duration_ms,
                error_message=str(e)
            )

    async def test_model_availability(self, model_name: str) -> TestResult:
        """Test individual model availability"""
        start_time = time.time()
        
        try:
            url = f"{self.ollama_url}/api/generate"
            payload = {
                "model": model_name,
                "prompt": "Test connectivity. Respond with 'OK'.",
                "stream": False,
                "options": {"max_tokens": 5, "temperature": 0.1}
            }
            
            async with self.session.post(url, json=payload, timeout=30) as response:
                duration_ms = (time.time() - start_time) * 1000
                
                if response.status == 200:
                    result = await response.json()
                    response_text = result.get('response', '').strip()
                    
                    return TestResult(
                        test_name=f"model_availability_{model_name}",
                        status="PASS",
                        duration_ms=duration_ms,
                        details={
                            'model_name': model_name,
                            'response_text': response_text,
                            'tokens_used': result.get('eval_count', 0)
                        }
                    )
                else:
                    return TestResult(
                        test_name=f"model_availability_{model_name}",
                        status="FAIL",
                        duration_ms=duration_ms,
                        error_message=f"HTTP {response.status}"
                    )
                    
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            return TestResult(
                test_name=f"model_availability_{model_name}",
                status="ERROR",
                duration_ms=duration_ms,
                error_message=str(e)
            )

    async def test_concurrent_model_calls(self) -> TestResult:
        """Test concurrent calls to multiple models"""
        start_time = time.time()

        try:
            # Select subset of models for concurrent testing
            test_models = [m['name'] for m in self.test_models[:5]]  # Test first 5 models

            # Create concurrent tasks
            tasks = []
            for model_name in test_models:
                task = self.test_model_availability(model_name)
                tasks.append(task)

            # Execute concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Analyze results
            successful_calls = 0
            failed_calls = 0
            total_response_time = 0

            for result in results:
                if isinstance(result, TestResult):
                    if result.status == "PASS":
                        successful_calls += 1
                        total_response_time += result.duration_ms
                    else:
                        failed_calls += 1
                else:
                    failed_calls += 1

            avg_response_time = total_response_time / successful_calls if successful_calls > 0 else 0
            success_rate = successful_calls / len(test_models) * 100

            duration_ms = (time.time() - start_time) * 1000

            return TestResult(
                test_name="concurrent_model_calls",
                status="PASS" if success_rate >= 80 else "FAIL",
                duration_ms=duration_ms,
                details={
                    'models_tested': len(test_models),
                    'successful_calls': successful_calls,
                    'failed_calls': failed_calls,
                    'success_rate': success_rate,
                    'avg_response_time_ms': avg_response_time
                }
            )

        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            return TestResult(
                test_name="concurrent_model_calls",
                status="ERROR",
                duration_ms=duration_ms,
                error_message=str(e)
            )

    def test_system_performance(self) -> TestResult:
        """Test system performance metrics"""
        start_time = time.time()

        try:
            # Get system metrics
            cpu_usage = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')

            # Performance thresholds
            cpu_threshold = 80.0
            memory_threshold = 85.0
            disk_threshold = 90.0

            # Check performance
            performance_issues = []
            if cpu_usage > cpu_threshold:
                performance_issues.append(f"High CPU usage: {cpu_usage:.1f}%")
            if memory.percent > memory_threshold:
                performance_issues.append(f"High memory usage: {memory.percent:.1f}%")
            if disk.percent > disk_threshold:
                performance_issues.append(f"High disk usage: {disk.percent:.1f}%")

            duration_ms = (time.time() - start_time) * 1000

            return TestResult(
                test_name="system_performance",
                status="PASS" if not performance_issues else "FAIL",
                duration_ms=duration_ms,
                details={
                    'cpu_usage': cpu_usage,
                    'memory_usage': memory.percent,
                    'disk_usage': disk.percent,
                    'performance_issues': performance_issues,
                    'available_memory_gb': memory.available / (1024**3)
                }
            )

        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            return TestResult(
                test_name="system_performance",
                status="ERROR",
                duration_ms=duration_ms,
                error_message=str(e)
            )

    async def test_stress_testing(self) -> TestResult:
        """Perform stress testing on the system"""
        start_time = time.time()

        try:
            # Stress test parameters
            num_concurrent_requests = 10
            num_iterations = 5

            stress_results = []

            for iteration in range(num_iterations):
                iteration_start = time.time()

                # Create multiple concurrent tasks
                tasks = []
                for i in range(num_concurrent_requests):
                    # Alternate between different types of operations
                    if i % 3 == 0:
                        task = self.test_model_availability(self.test_models[0]['name'])
                    elif i % 3 == 1:
                        task = self.test_database_operations()
                    else:
                        task = asyncio.create_task(asyncio.sleep(0.1))  # Simulate other operations

                    tasks.append(task)

                # Execute all tasks concurrently
                results = await asyncio.gather(*tasks, return_exceptions=True)

                iteration_duration = (time.time() - iteration_start) * 1000

                # Count successful operations
                successful_ops = sum(1 for r in results if isinstance(r, TestResult) and r.status == "PASS")

                stress_results.append({
                    'iteration': iteration + 1,
                    'duration_ms': iteration_duration,
                    'successful_ops': successful_ops,
                    'total_ops': num_concurrent_requests
                })

            # Analyze stress test results
            avg_duration = sum(r['duration_ms'] for r in stress_results) / len(stress_results)
            total_successful = sum(r['successful_ops'] for r in stress_results)
            total_operations = sum(r['total_ops'] for r in stress_results)
            success_rate = (total_successful / total_operations) * 100

            duration_ms = (time.time() - start_time) * 1000

            return TestResult(
                test_name="stress_testing",
                status="PASS" if success_rate >= 70 else "FAIL",
                duration_ms=duration_ms,
                details={
                    'iterations': num_iterations,
                    'concurrent_requests': num_concurrent_requests,
                    'avg_iteration_duration_ms': avg_duration,
                    'total_operations': total_operations,
                    'successful_operations': total_successful,
                    'success_rate': success_rate,
                    'detailed_results': stress_results
                }
            )

        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            return TestResult(
                test_name="stress_testing",
                status="ERROR",
                duration_ms=duration_ms,
                error_message=str(e)
            )

    async def test_error_handling_and_recovery(self) -> TestResult:
        """Test error handling and recovery mechanisms"""
        start_time = time.time()

        try:
            error_scenarios = []

            # Test 1: Invalid model name
            try:
                result = await self.test_model_availability("invalid_model_name_12345")
                error_scenarios.append({
                    'scenario': 'invalid_model',
                    'handled_gracefully': result.status in ['FAIL', 'ERROR'],
                    'error_message': result.error_message
                })
            except Exception as e:
                error_scenarios.append({
                    'scenario': 'invalid_model',
                    'handled_gracefully': False,
                    'error_message': str(e)
                })

            # Test 2: Database connection with invalid path
            try:
                invalid_conn = sqlite3.connect("/invalid/path/database.db")
                invalid_conn.close()
                error_scenarios.append({
                    'scenario': 'invalid_database',
                    'handled_gracefully': False,
                    'error_message': 'Should have failed'
                })
            except Exception as e:
                error_scenarios.append({
                    'scenario': 'invalid_database',
                    'handled_gracefully': True,
                    'error_message': str(e)
                })

            # Test 3: Network timeout simulation
            try:
                # This should timeout
                url = f"{self.ollama_url}/api/generate"
                payload = {
                    "model": self.test_models[0]['name'],
                    "prompt": "This is a timeout test",
                    "stream": False
                }

                async with self.session.post(url, json=payload, timeout=0.001) as response:
                    pass

                error_scenarios.append({
                    'scenario': 'network_timeout',
                    'handled_gracefully': False,
                    'error_message': 'Should have timed out'
                })
            except asyncio.TimeoutError:
                error_scenarios.append({
                    'scenario': 'network_timeout',
                    'handled_gracefully': True,
                    'error_message': 'Timeout handled correctly'
                })
            except Exception as e:
                error_scenarios.append({
                    'scenario': 'network_timeout',
                    'handled_gracefully': True,
                    'error_message': str(e)
                })

            # Analyze error handling
            gracefully_handled = sum(1 for scenario in error_scenarios if scenario['handled_gracefully'])
            total_scenarios = len(error_scenarios)
            error_handling_rate = (gracefully_handled / total_scenarios) * 100

            duration_ms = (time.time() - start_time) * 1000

            return TestResult(
                test_name="error_handling_recovery",
                status="PASS" if error_handling_rate >= 80 else "FAIL",
                duration_ms=duration_ms,
                details={
                    'scenarios_tested': total_scenarios,
                    'gracefully_handled': gracefully_handled,
                    'error_handling_rate': error_handling_rate,
                    'scenario_details': error_scenarios
                }
            )

        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            return TestResult(
                test_name="error_handling_recovery",
                status="ERROR",
                duration_ms=duration_ms,
                error_message=str(e)
            )

    def save_test_results(self, test_results: List[TestResult], session_id: str):
        """Save test results to database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            for result in test_results:
                cursor.execute('''
                    INSERT INTO test_results
                    (test_name, test_category, component_name, status, duration_ms,
                     details, error_message, timestamp, test_session_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    result.test_name,
                    self._get_test_category(result.test_name),
                    self._get_component_name(result.test_name),
                    result.status,
                    result.duration_ms,
                    json.dumps(result.details),
                    result.error_message,
                    result.timestamp,
                    session_id
                ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"❌ Error saving test results: {e}")

    def _get_test_category(self, test_name: str) -> str:
        """Get test category from test name"""
        if 'model' in test_name:
            return 'model_testing'
        elif 'database' in test_name:
            return 'database_testing'
        elif 'portfolio' in test_name:
            return 'portfolio_testing'
        elif 'market' in test_name:
            return 'market_testing'
        elif 'concurrent' in test_name:
            return 'concurrent_testing'
        elif 'stress' in test_name:
            return 'stress_testing'
        elif 'error' in test_name:
            return 'error_testing'
        elif 'performance' in test_name:
            return 'performance_testing'
        else:
            return 'general_testing'

    def _get_component_name(self, test_name: str) -> str:
        """Get component name from test name"""
        if 'ollama' in test_name:
            return 'ollama_server'
        elif 'model' in test_name:
            return 'ai_models'
        elif 'database' in test_name:
            return 'database'
        elif 'portfolio' in test_name:
            return 'portfolio_manager'
        elif 'market' in test_name:
            return 'market_simulation'
        else:
            return 'system'

    def display_test_results(self, test_results: List[TestResult], session_id: str):
        """Display comprehensive test results"""
        print(f"\n🚀 EXTENDED FULL COVERAGE TESTING RESULTS - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 120)

        # Overall statistics
        total_tests = len(test_results)
        passed_tests = sum(1 for r in test_results if r.status == "PASS")
        failed_tests = sum(1 for r in test_results if r.status == "FAIL")
        error_tests = sum(1 for r in test_results if r.status == "ERROR")

        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        avg_duration = sum(r.duration_ms for r in test_results) / total_tests if total_tests > 0 else 0

        print(f"📊 OVERALL TEST STATISTICS:")
        print(f"   Session ID: {session_id}")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {passed_tests} ✅")
        print(f"   Failed: {failed_tests} ❌")
        print(f"   Errors: {error_tests} 🔥")
        print(f"   Success Rate: {success_rate:.1f}%")
        print(f"   Average Duration: {avg_duration:.1f}ms")

        # Test results by category
        categories = {}
        for result in test_results:
            category = self._get_test_category(result.test_name)
            if category not in categories:
                categories[category] = {'total': 0, 'passed': 0, 'failed': 0, 'errors': 0}

            categories[category]['total'] += 1
            if result.status == "PASS":
                categories[category]['passed'] += 1
            elif result.status == "FAIL":
                categories[category]['failed'] += 1
            else:
                categories[category]['errors'] += 1

        print(f"\n📋 TEST RESULTS BY CATEGORY:")
        print(f"{'Category':<25} {'Total':<7} {'Passed':<8} {'Failed':<8} {'Errors':<8} {'Success Rate':<12}")
        print("-" * 120)

        for category, stats in sorted(categories.items()):
            success_rate_cat = (stats['passed'] / stats['total']) * 100 if stats['total'] > 0 else 0
            status_emoji = "✅" if success_rate_cat >= 80 else "⚠️" if success_rate_cat >= 60 else "❌"

            print(f"{category:<25} {stats['total']:<7} {stats['passed']:<8} {stats['failed']:<8} "
                  f"{stats['errors']:<8} {success_rate_cat:>10.1f}% {status_emoji}")

        # Model-specific results
        model_results = [r for r in test_results if 'model' in r.test_name]
        if model_results:
            print(f"\n🤖 MODEL TESTING RESULTS:")
            print(f"{'Model Name':<25} {'Availability':<12} {'Trading Decision':<17} {'Avg Response Time':<18}")
            print("-" * 120)

            model_stats = {}
            for result in model_results:
                # Extract model name from test name
                if '_' in result.test_name:
                    parts = result.test_name.split('_')
                    if len(parts) >= 3:
                        model_name = '_'.join(parts[2:])  # Everything after "model_availability_" or "trading_decision_"

                        if model_name not in model_stats:
                            model_stats[model_name] = {'availability': 'N/A', 'trading': 'N/A', 'response_times': []}

                        if 'availability' in result.test_name:
                            model_stats[model_name]['availability'] = result.status
                            model_stats[model_name]['response_times'].append(result.duration_ms)
                        elif 'trading' in result.test_name:
                            model_stats[model_name]['trading'] = result.status
                            model_stats[model_name]['response_times'].append(result.duration_ms)

            for model_name, stats in sorted(model_stats.items()):
                avg_response = sum(stats['response_times']) / len(stats['response_times']) if stats['response_times'] else 0

                availability_emoji = "✅" if stats['availability'] == "PASS" else "❌" if stats['availability'] == "FAIL" else "⚠️"
                trading_emoji = "✅" if stats['trading'] == "PASS" else "❌" if stats['trading'] == "FAIL" else "⚠️"

                # Check if this is a new model
                new_indicator = "⭐" if model_name in ['phi4-reasoning:plus', 'nemotron-mini:4b'] else ""

                print(f"{model_name:<25} {stats['availability']:<8} {availability_emoji:<4} "
                      f"{stats['trading']:<8} {trading_emoji:<9} {avg_response:>15.1f}ms {new_indicator}")

        # Performance metrics
        performance_results = [r for r in test_results if 'performance' in r.test_name or 'stress' in r.test_name]
        if performance_results:
            print(f"\n⚡ PERFORMANCE TEST RESULTS:")
            for result in performance_results:
                status_emoji = "✅" if result.status == "PASS" else "❌" if result.status == "FAIL" else "🔥"
                print(f"   {result.test_name:<30} {result.status:<8} {status_emoji} ({result.duration_ms:.1f}ms)")

                if result.details:
                    if 'cpu_usage' in result.details:
                        print(f"      CPU Usage: {result.details['cpu_usage']:.1f}%")
                    if 'memory_usage' in result.details:
                        print(f"      Memory Usage: {result.details['memory_usage']:.1f}%")
                    if 'success_rate' in result.details:
                        print(f"      Success Rate: {result.details['success_rate']:.1f}%")

        # Error analysis
        error_results = [r for r in test_results if r.status in ["FAIL", "ERROR"]]
        if error_results:
            print(f"\n🔥 ERROR ANALYSIS:")
            print(f"{'Test Name':<35} {'Status':<8} {'Error Message':<50}")
            print("-" * 120)

            for result in error_results:
                error_msg = result.error_message[:47] + "..." if len(result.error_message) > 50 else result.error_message
                print(f"{result.test_name:<35} {result.status:<8} {error_msg:<50}")

        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        if success_rate >= 90:
            print("   🟢 Excellent! System is performing very well.")
        elif success_rate >= 80:
            print("   🟡 Good performance with minor issues to address.")
        elif success_rate >= 70:
            print("   🟠 Moderate performance. Several issues need attention.")
        else:
            print("   🔴 Poor performance. Significant issues require immediate attention.")

        if failed_tests > 0:
            print(f"   • Address {failed_tests} failed tests")
        if error_tests > 0:
            print(f"   • Fix {error_tests} error conditions")

        # System health recommendations
        perf_result = next((r for r in test_results if r.test_name == "system_performance"), None)
        if perf_result and perf_result.details:
            if perf_result.details.get('cpu_usage', 0) > 70:
                print("   • High CPU usage detected - consider optimizing or scaling")
            if perf_result.details.get('memory_usage', 0) > 80:
                print("   • High memory usage detected - monitor for memory leaks")

        print("=" * 120)

    async def run_extended_full_coverage_testing(self, test_duration_minutes: int = 60):
        """Run extended full coverage testing"""
        session_id = f"test_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        logger.info(f"🚀 Starting EXTENDED FULL COVERAGE TESTING")
        logger.info(f"   Session ID: {session_id}")
        logger.info(f"   Duration: {test_duration_minutes} minutes")
        logger.info(f"   Models to test: {len(self.test_models)}")
        logger.info(f"   Test categories: {len(self.test_categories)}")

        self.session = aiohttp.ClientSession()
        self.testing_active = True
        self.test_start_time = datetime.now()

        all_test_results = []

        try:
            print(f"\n🧪 PHASE 1: BASIC CONNECTIVITY AND AVAILABILITY TESTING")
            print("-" * 80)

            # Test Ollama server connectivity
            result = await self.test_ollama_server_connectivity()
            all_test_results.append(result)
            print(f"   Ollama Server: {result.status} ({result.duration_ms:.1f}ms)")

            # Test database operations
            result = self.test_database_operations()
            all_test_results.append(result)
            print(f"   Database: {result.status} ({result.duration_ms:.1f}ms)")

            # Test system performance
            result = self.test_system_performance()
            all_test_results.append(result)
            print(f"   System Performance: {result.status} ({result.duration_ms:.1f}ms)")

            print(f"\n🤖 PHASE 2: MODEL AVAILABILITY TESTING")
            print("-" * 80)

            # Test all models for availability
            for model in self.test_models:
                result = await self.test_model_availability(model['name'])
                all_test_results.append(result)

                status_emoji = "✅" if result.status == "PASS" else "❌" if result.status == "FAIL" else "🔥"
                new_indicator = "⭐" if model['name'] in ['phi4-reasoning:plus', 'nemotron-mini:4b'] else ""

                print(f"   {model['name']:<25} {result.status:<8} {status_emoji} ({result.duration_ms:.1f}ms) {new_indicator}")

                # Rate limiting
                await asyncio.sleep(1)

            print(f"\n🎯 PHASE 3: TRADING DECISION TESTING")
            print("-" * 80)

            # Test trading decisions for priority models
            priority_models = [m for m in self.test_models if m['priority'] in ['HIGH', 'MEDIUM']]

            for model in priority_models:
                result = await self.test_model_trading_decision(model['name'])
                all_test_results.append(result)

                status_emoji = "✅" if result.status == "PASS" else "❌" if result.status == "FAIL" else "🔥"
                quality_score = result.details.get('quality_score', 0) if result.details else 0

                print(f"   {model['name']:<25} {result.status:<8} {status_emoji} Quality: {quality_score:.1f}%")

                # Rate limiting
                await asyncio.sleep(2)

            print(f"\n🔧 PHASE 4: SYSTEM COMPONENT TESTING")
            print("-" * 80)

            # Test market simulation
            result = self.test_market_simulation()
            all_test_results.append(result)
            print(f"   Market Simulation: {result.status} ({result.duration_ms:.1f}ms)")

            # Test portfolio management
            result = self.test_portfolio_management()
            all_test_results.append(result)
            print(f"   Portfolio Management: {result.status} ({result.duration_ms:.1f}ms)")

            print(f"\n⚡ PHASE 5: PERFORMANCE AND STRESS TESTING")
            print("-" * 80)

            # Test concurrent model calls
            result = await self.test_concurrent_model_calls()
            all_test_results.append(result)
            print(f"   Concurrent Calls: {result.status} ({result.duration_ms:.1f}ms)")

            # Test stress testing
            result = await self.test_stress_testing()
            all_test_results.append(result)
            print(f"   Stress Testing: {result.status} ({result.duration_ms:.1f}ms)")

            print(f"\n🛡️ PHASE 6: ERROR HANDLING AND RECOVERY TESTING")
            print("-" * 80)

            # Test error handling
            result = await self.test_error_handling_and_recovery()
            all_test_results.append(result)
            print(f"   Error Handling: {result.status} ({result.duration_ms:.1f}ms)")

            # Save all results
            self.save_test_results(all_test_results, session_id)

            # Display comprehensive results
            self.display_test_results(all_test_results, session_id)

            # Final summary
            total_duration = (datetime.now() - self.test_start_time).total_seconds()
            total_tests = len(all_test_results)
            passed_tests = sum(1 for r in all_test_results if r.status == "PASS")

            print(f"\n🏁 EXTENDED FULL COVERAGE TESTING COMPLETED")
            print(f"   Total Duration: {total_duration:.1f} seconds")
            print(f"   Total Tests: {total_tests}")
            print(f"   Tests Passed: {passed_tests}")
            print(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")
            print(f"   Database: {self.db_path}")

        except KeyboardInterrupt:
            logger.info("🛑 Testing stopped by user")
        finally:
            self.testing_active = False
            await self.session.close()

async def main():
    """Main function for extended full coverage testing"""
    print("🚀 EXTENDED FULL COVERAGE TESTING INTEGRATION SYSTEM")
    print("=" * 80)
    print("Comprehensive testing for:")
    print("• All 13 of your exact Ollama models")
    print("• System components and integrations")
    print("• Performance and stress testing")
    print("• Error handling and recovery")
    print("• Concurrent operations")
    print("• Database operations")
    print("• Market simulation")
    print("• Portfolio management")
    print("• Complete system validation")
    print("=" * 80)

    system = ExtendedFullCoverageTestingSystem()

    print(f"\n🤖 YOUR 13 MODELS TO BE TESTED:")
    for model in system.test_models:
        priority_emoji = "🔥" if model['priority'] == 'HIGH' else "⚡" if model['priority'] == 'MEDIUM' else "📝"
        new_indicator = "⭐ NEW!" if model['name'] in ['phi4-reasoning:plus', 'nemotron-mini:4b'] else ""
        print(f"   {model['name']:<25} | {model['size_gb']:>6.1f}GB | {model['family']:<12} | {model['priority']:<6} {priority_emoji} {new_indicator}")

    print(f"\n🎯 Choose testing scope:")
    print(f"1. Quick validation (15 minutes)")
    print(f"2. Standard testing (60 minutes)")
    print(f"3. Extended testing (120 minutes)")
    print(f"4. Custom duration")

    try:
        choice = input("Enter choice (1-4, default=2): ").strip()
        if choice == "1":
            duration = 15
        elif choice == "3":
            duration = 120
        elif choice == "4":
            duration = int(input("Enter duration in minutes: "))
        else:
            duration = 60
    except:
        duration = 60

    print(f"\n🚀 Starting extended full coverage testing for {duration} minutes...")
    print(f"This will test EVERYTHING comprehensively!")
    print(f"Press Ctrl+C to stop early")

    # Run extended testing
    await system.run_extended_full_coverage_testing(test_duration_minutes=duration)

    print(f"\n✅ Extended full coverage testing completed!")

if __name__ == "__main__":
    asyncio.run(main())

    async def test_model_trading_decision(self, model_name: str) -> TestResult:
        """Test model's trading decision capability"""
        start_time = time.time()
        
        try:
            prompt = f"""You are an AI trader using {model_name}.

MARKET DATA:
- BTCUSDT: $97,234 (+1.2%)
- ETHUSDT: $3,345 (+0.8%)
- SOLUSDT: $189 (+2.1%)

Make a trading decision:
DECISION: [BUY/SELL/HOLD]
SYMBOL: [symbol or NONE]
CONFIDENCE: [0-100]
REASONING: [brief reason]

Be specific and decisive."""

            url = f"{self.ollama_url}/api/generate"
            payload = {
                "model": model_name,
                "prompt": prompt,
                "stream": False,
                "options": {"max_tokens": 300, "temperature": 0.2}
            }
            
            async with self.session.post(url, json=payload, timeout=90) as response:
                duration_ms = (time.time() - start_time) * 1000
                
                if response.status == 200:
                    result = await response.json()
                    response_text = result.get('response', '')
                    
                    # Parse decision
                    decision = self._parse_trading_decision(response_text)
                    
                    # Evaluate decision quality
                    quality_score = self._evaluate_decision_quality(decision, response_text)
                    
                    return TestResult(
                        test_name=f"trading_decision_{model_name}",
                        status="PASS" if decision else "FAIL",
                        duration_ms=duration_ms,
                        details={
                            'model_name': model_name,
                            'decision': decision,
                            'quality_score': quality_score,
                            'response_length': len(response_text),
                            'tokens_used': result.get('eval_count', 0)
                        }
                    )
                else:
                    return TestResult(
                        test_name=f"trading_decision_{model_name}",
                        status="FAIL",
                        duration_ms=duration_ms,
                        error_message=f"HTTP {response.status}"
                    )
                    
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            return TestResult(
                test_name=f"trading_decision_{model_name}",
                status="ERROR",
                duration_ms=duration_ms,
                error_message=str(e)
            )

    def _parse_trading_decision(self, response: str) -> Optional[Dict[str, Any]]:
        """Parse trading decision from AI response"""
        try:
            lines = response.strip().split('\n')
            decision = {}
            
            for line in lines:
                line = line.strip()
                if line.startswith('DECISION:'):
                    decision['action'] = line.split(':', 1)[1].strip().upper()
                elif line.startswith('SYMBOL:'):
                    symbol = line.split(':', 1)[1].strip().upper()
                    decision['symbol'] = symbol if symbol != 'NONE' else None
                elif line.startswith('CONFIDENCE:'):
                    try:
                        confidence = float(line.split(':', 1)[1].strip())
                        decision['confidence'] = min(100, max(0, confidence))
                    except:
                        decision['confidence'] = 0
                elif line.startswith('REASONING:'):
                    decision['reasoning'] = line.split(':', 1)[1].strip()
            
            if 'action' in decision and decision['action'] in ['BUY', 'SELL', 'HOLD']:
                return decision
            else:
                return None
                
        except Exception:
            return None

    def _evaluate_decision_quality(self, decision: Optional[Dict[str, Any]], response: str) -> float:
        """Evaluate the quality of a trading decision"""
        if not decision:
            return 0.0
        
        score = 0.0
        
        # Check if decision is valid
        if decision.get('action') in ['BUY', 'SELL', 'HOLD']:
            score += 25
        
        # Check confidence level
        confidence = decision.get('confidence', 0)
        if 50 <= confidence <= 90:
            score += 25
        elif confidence > 0:
            score += 10
        
        # Check reasoning quality
        reasoning = decision.get('reasoning', '')
        if len(reasoning) > 20:
            score += 25
        elif len(reasoning) > 0:
            score += 10
        
        # Check symbol selection
        if decision.get('symbol'):
            score += 15
        
        # Check response structure
        if 'DECISION:' in response and 'CONFIDENCE:' in response:
            score += 10
        
        return min(100.0, score)

    async def test_database_operations(self) -> TestResult:
        """Test database operations"""
        start_time = time.time()
        
        try:
            # Test database connection
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Test insert
            test_data = {
                'timestamp': datetime.now().isoformat(),
                'test_value': random.randint(1, 1000)
            }
            
            cursor.execute('''
                INSERT INTO test_results (test_name, test_category, status, timestamp)
                VALUES (?, ?, ?, ?)
            ''', ('db_test', 'database', 'PASS', test_data['timestamp']))
            
            # Test select
            cursor.execute('SELECT COUNT(*) FROM test_results')
            count = cursor.fetchone()[0]
            
            # Test update
            cursor.execute('''
                UPDATE test_results SET details = ? WHERE test_name = ?
            ''', (json.dumps(test_data), 'db_test'))
            
            conn.commit()
            conn.close()
            
            duration_ms = (time.time() - start_time) * 1000
            
            return TestResult(
                test_name="database_operations",
                status="PASS",
                duration_ms=duration_ms,
                details={
                    'operations_tested': ['connect', 'insert', 'select', 'update'],
                    'record_count': count,
                    'test_data': test_data
                }
            )
            
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            return TestResult(
                test_name="database_operations",
                status="ERROR",
                duration_ms=duration_ms,
                error_message=str(e)
            )

    def test_market_simulation(self) -> TestResult:
        """Test market simulation functionality"""
        start_time = time.time()
        
        try:
            # Simulate market data generation
            crypto_pairs = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT']
            market_data = {}
            
            for symbol in crypto_pairs:
                # Generate realistic market data
                base_price = random.uniform(100, 100000)
                change_24h = random.uniform(-10, 10)
                volume = random.uniform(1000000, 50000000)
                rsi = random.uniform(20, 80)
                
                market_data[symbol] = {
                    'price': base_price,
                    'change_24h': change_24h,
                    'volume': volume,
                    'rsi': rsi,
                    'timestamp': datetime.now().isoformat()
                }
            
            # Validate market data
            validation_passed = True
            for symbol, data in market_data.items():
                if not all(key in data for key in ['price', 'change_24h', 'volume', 'rsi']):
                    validation_passed = False
                    break
                if data['price'] <= 0 or data['volume'] <= 0:
                    validation_passed = False
                    break
            
            duration_ms = (time.time() - start_time) * 1000
            
            return TestResult(
                test_name="market_simulation",
                status="PASS" if validation_passed else "FAIL",
                duration_ms=duration_ms,
                details={
                    'symbols_generated': len(market_data),
                    'validation_passed': validation_passed,
                    'sample_data': {k: v for k, v in list(market_data.items())[:2]}
                }
            )
            
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            return TestResult(
                test_name="market_simulation",
                status="ERROR",
                duration_ms=duration_ms,
                error_message=str(e)
            )

    def test_portfolio_management(self) -> TestResult:
        """Test portfolio management functionality"""
        start_time = time.time()
        
        try:
            # Initialize test portfolio
            portfolio = {
                'cash': 10000.0,
                'positions': {},
                'trades': []
            }
            
            # Test buy operation
            symbol = 'BTCUSDT'
            price = 50000.0
            amount = 5000.0
            quantity = amount / price
            
            # Execute buy
            portfolio['cash'] -= amount
            portfolio['positions'][symbol] = {
                'quantity': quantity,
                'avg_price': price
            }
            
            # Test portfolio value calculation
            current_price = 52000.0  # Price increased
            position_value = portfolio['positions'][symbol]['quantity'] * current_price
            total_value = portfolio['cash'] + position_value
            
            # Test sell operation
            sell_quantity = quantity * 0.5
            sell_value = sell_quantity * current_price
            pnl = sell_value - (sell_quantity * price)
            
            portfolio['cash'] += sell_value
            portfolio['positions'][symbol]['quantity'] -= sell_quantity
            
            # Validate portfolio state
            validation_passed = (
                portfolio['cash'] > 0 and
                portfolio['positions'][symbol]['quantity'] > 0 and
                pnl > 0  # Should be profitable
            )
            
            duration_ms = (time.time() - start_time) * 1000
            
            return TestResult(
                test_name="portfolio_management",
                status="PASS" if validation_passed else "FAIL",
                duration_ms=duration_ms,
                details={
                    'operations_tested': ['buy', 'sell', 'value_calculation'],
                    'final_cash': portfolio['cash'],
                    'final_position': portfolio['positions'][symbol]['quantity'],
                    'pnl': pnl,
                    'total_value': total_value
                }
            )
            
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            return TestResult(
                test_name="portfolio_management",
                status="ERROR",
                duration_ms=duration_ms,
                error_message=str(e)
            )
