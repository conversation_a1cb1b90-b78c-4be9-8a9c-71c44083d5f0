#!/usr/bin/env python3
"""
Comprehensive System Status Checker and Development Continuation Script
Real-time analysis of NORYON V2 system components and continuation planning
"""

import asyncio
import logging
import sys
import time
import json
import os
import subprocess
import sqlite3
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
from pathlib import Path
import traceback

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'system_status_check_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("SystemStatusChecker")


class ComprehensiveSystemStatusChecker:
    """
    Comprehensive system status checker and development continuation planner.
    
    Analyzes:
    - Current system components and their status
    - Available AI models and integration status
    - Database systems and data integrity
    - Testing framework completeness
    - Performance metrics and optimization opportunities
    - Development roadmap and next steps
    """
    
    def __init__(self):
        self.status_report = {}
        self.development_plan = {}
        self.test_results = {}
        self.performance_metrics = {}
        self.start_time = datetime.now(timezone.utc)
        
        # System components to check
        self.components = {
            'ai_models': ['marco-o1:7b', 'magistral:24b', 'command-r:35b', 'cogito:32b', 
                         'gemma3:27b', 'mistral-small:24b', 'falcon3:10b', 'granite3.3:8b', 
                         'qwen3:32b', 'deepseek-r1:32b', 'phi4-reasoning:plus', 'nemotron-mini:4b'],
            'databases': ['postgres', 'redis', 'clickhouse', 'mongodb'],
            'core_modules': ['advanced_ml_engine', 'advanced_technical_analysis', 
                           'advanced_strategy_engine', 'comprehensive_testing_framework'],
            'trading_systems': ['portfolio_optimization', 'risk_management', 
                              'backtesting_engine', 'execution_engine']
        }
    
    async def run_comprehensive_status_check(self) -> Dict[str, Any]:
        """Run comprehensive system status check and development planning."""
        logger.info("🔍 STARTING COMPREHENSIVE SYSTEM STATUS CHECK")
        logger.info("=" * 80)
        
        try:
            # Phase 1: System Component Analysis
            logger.info("📊 Phase 1: Analyzing System Components...")
            await self._analyze_system_components()
            
            # Phase 2: AI Model Integration Check
            logger.info("🤖 Phase 2: Checking AI Model Integration...")
            await self._check_ai_model_integration()
            
            # Phase 3: Database Systems Check
            logger.info("🗄️ Phase 3: Verifying Database Systems...")
            await self._check_database_systems()
            
            # Phase 4: Testing Framework Analysis
            logger.info("🧪 Phase 4: Analyzing Testing Framework...")
            await self._analyze_testing_framework()
            
            # Phase 5: Performance Metrics Collection
            logger.info("⚡ Phase 5: Collecting Performance Metrics...")
            await self._collect_performance_metrics()
            
            # Phase 6: Development Roadmap Planning
            logger.info("🗺️ Phase 6: Planning Development Roadmap...")
            await self._plan_development_roadmap()
            
            # Phase 7: Generate Comprehensive Report
            logger.info("📄 Phase 7: Generating Comprehensive Report...")
            final_report = await self._generate_comprehensive_report()
            
            return final_report
            
        except Exception as e:
            logger.error(f"System status check error: {e}")
            return {"error": str(e), "traceback": traceback.format_exc()}
    
    async def _analyze_system_components(self):
        """Analyze current system components."""
        logger.info("🔍 Analyzing system components...")
        
        component_status = {}
        
        # Check Python files and modules
        python_files = list(Path('.').glob('*.py'))
        component_status['python_files'] = {
            'count': len(python_files),
            'files': [f.name for f in python_files[:20]]  # First 20 files
        }
        
        # Check core modules
        core_modules_status = {}
        for module in self.components['core_modules']:
            module_file = f"{module}.py"
            if Path(module_file).exists():
                try:
                    # Try to import the module
                    spec = __import__(module)
                    core_modules_status[module] = {
                        'status': 'available',
                        'file_size': Path(module_file).stat().st_size,
                        'last_modified': datetime.fromtimestamp(Path(module_file).stat().st_mtime).isoformat()
                    }
                except Exception as e:
                    core_modules_status[module] = {
                        'status': 'error',
                        'error': str(e)
                    }
            else:
                core_modules_status[module] = {'status': 'missing'}
        
        component_status['core_modules'] = core_modules_status
        
        # Check configuration files
        config_files = ['docker-compose.yml', 'requirements.txt', '.env.example']
        config_status = {}
        for config_file in config_files:
            config_status[config_file] = {
                'exists': Path(config_file).exists(),
                'size': Path(config_file).stat().st_size if Path(config_file).exists() else 0
            }
        
        component_status['configuration'] = config_status
        
        self.status_report['components'] = component_status
        logger.info(f"✅ Component analysis complete: {len(python_files)} Python files found")
    
    async def _check_ai_model_integration(self):
        """Check AI model integration status."""
        logger.info("🤖 Checking AI model integration...")
        
        ai_status = {}
        
        # Check Ollama availability
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                ai_status['ollama_available'] = True
                ai_status['ollama_models'] = result.stdout.strip().split('\n')[1:] if result.stdout.strip() else []
            else:
                ai_status['ollama_available'] = False
                ai_status['ollama_error'] = result.stderr
        except Exception as e:
            ai_status['ollama_available'] = False
            ai_status['ollama_error'] = str(e)
        
        # Check model integration in code
        model_integration = {}
        for model in self.components['ai_models']:
            # Search for model usage in Python files
            model_usage_count = 0
            for py_file in Path('.').glob('*.py'):
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if model in content:
                            model_usage_count += 1
                except Exception:
                    continue
            
            model_integration[model] = {
                'usage_count': model_usage_count,
                'integrated': model_usage_count > 0
            }
        
        ai_status['model_integration'] = model_integration
        
        self.status_report['ai_models'] = ai_status
        logger.info(f"✅ AI model check complete: Ollama available: {ai_status.get('ollama_available', False)}")
    
    async def _check_database_systems(self):
        """Check database systems status."""
        logger.info("🗄️ Checking database systems...")
        
        db_status = {}
        
        # Check SQLite databases
        sqlite_dbs = list(Path('.').glob('*.db'))
        db_status['sqlite_databases'] = {
            'count': len(sqlite_dbs),
            'databases': [db.name for db in sqlite_dbs]
        }
        
        # Check Docker services
        try:
            result = subprocess.run(['docker', 'ps'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                db_status['docker_available'] = True
                db_status['running_containers'] = len(result.stdout.strip().split('\n')) - 1
            else:
                db_status['docker_available'] = False
        except Exception as e:
            db_status['docker_available'] = False
            db_status['docker_error'] = str(e)
        
        self.status_report['databases'] = db_status
        logger.info(f"✅ Database check complete: {len(sqlite_dbs)} SQLite databases found")
    
    async def _analyze_testing_framework(self):
        """Analyze testing framework completeness."""
        logger.info("🧪 Analyzing testing framework...")
        
        testing_status = {}
        
        # Check test files
        test_files = list(Path('.').glob('test*.py')) + list(Path('.').glob('*test*.py'))
        testing_status['test_files'] = {
            'count': len(test_files),
            'files': [f.name for f in test_files]
        }
        
        # Check tests directory
        tests_dir = Path('tests')
        if tests_dir.exists():
            test_subdirs = [d.name for d in tests_dir.iterdir() if d.is_dir()]
            testing_status['tests_directory'] = {
                'exists': True,
                'subdirectories': test_subdirs
            }
        else:
            testing_status['tests_directory'] = {'exists': False}
        
        self.status_report['testing'] = testing_status
        logger.info(f"✅ Testing analysis complete: {len(test_files)} test files found")
    
    async def _collect_performance_metrics(self):
        """Collect performance metrics."""
        logger.info("⚡ Collecting performance metrics...")
        
        metrics = {
            'system_info': {
                'python_version': sys.version,
                'platform': sys.platform,
                'current_time': datetime.now(timezone.utc).isoformat()
            },
            'file_metrics': {
                'total_python_files': len(list(Path('.').glob('*.py'))),
                'total_size_mb': sum(f.stat().st_size for f in Path('.').glob('*.py')) / (1024 * 1024)
            }
        }
        
        self.performance_metrics = metrics
        logger.info("✅ Performance metrics collected")
    
    async def _plan_development_roadmap(self):
        """Plan development roadmap based on analysis."""
        logger.info("🗺️ Planning development roadmap...")
        
        roadmap = {
            'immediate_priorities': [],
            'short_term_goals': [],
            'long_term_objectives': [],
            'recommended_actions': []
        }
        
        # Analyze current state and plan next steps
        if self.status_report.get('ai_models', {}).get('ollama_available', False):
            roadmap['immediate_priorities'].append("✅ Ollama integration ready - activate AI agents")
        else:
            roadmap['immediate_priorities'].append("🔧 Install and configure Ollama for AI agents")
        
        if self.status_report.get('databases', {}).get('docker_available', False):
            roadmap['immediate_priorities'].append("✅ Docker ready - start database services")
        else:
            roadmap['immediate_priorities'].append("🔧 Install Docker for database services")
        
        # Add comprehensive testing
        roadmap['short_term_goals'].extend([
            "🧪 Run comprehensive integration tests",
            "📊 Performance optimization and monitoring",
            "🤖 AI agent coordination enhancement",
            "📈 Advanced trading strategy implementation"
        ])
        
        # Add long-term objectives
        roadmap['long_term_objectives'].extend([
            "🚀 Production deployment preparation",
            "🔄 Continuous integration/deployment setup",
            "📱 Mobile app development",
            "🌐 Multi-exchange integration expansion"
        ])
        
        self.development_plan = roadmap
        logger.info("✅ Development roadmap planned")
    
    async def _generate_comprehensive_report(self) -> Dict[str, Any]:
        """Generate comprehensive status report."""
        logger.info("📄 Generating comprehensive report...")
        
        end_time = datetime.now(timezone.utc)
        duration = (end_time - self.start_time).total_seconds()
        
        report = {
            'timestamp': end_time.isoformat(),
            'duration_seconds': duration,
            'system_status': self.status_report,
            'performance_metrics': self.performance_metrics,
            'development_plan': self.development_plan,
            'summary': {
                'total_components_checked': len(self.status_report),
                'status': 'COMPREHENSIVE_ANALYSIS_COMPLETE'
            }
        }
        
        # Save report to file
        report_file = f'comprehensive_status_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"✅ Comprehensive report saved to: {report_file}")
        return report


async def main():
    """Main execution function."""
    print("🚀 NORYON V2 COMPREHENSIVE SYSTEM STATUS CHECKER")
    print("=" * 80)
    
    checker = ComprehensiveSystemStatusChecker()
    report = await checker.run_comprehensive_status_check()
    
    print("\n📊 SYSTEM STATUS SUMMARY:")
    print("=" * 50)
    
    if 'error' in report:
        print(f"❌ Error: {report['error']}")
        return
    
    # Display key findings
    components = report.get('system_status', {}).get('components', {})
    ai_models = report.get('system_status', {}).get('ai_models', {})
    databases = report.get('system_status', {}).get('databases', {})
    
    print(f"📁 Python Files: {components.get('python_files', {}).get('count', 0)}")
    print(f"🤖 Ollama Available: {ai_models.get('ollama_available', False)}")
    print(f"🐳 Docker Available: {databases.get('docker_available', False)}")
    print(f"🗄️ SQLite DBs: {databases.get('sqlite_databases', {}).get('count', 0)}")
    
    print("\n🗺️ DEVELOPMENT ROADMAP:")
    print("=" * 50)
    
    roadmap = report.get('development_plan', {})
    for priority in roadmap.get('immediate_priorities', []):
        print(f"  {priority}")
    
    print(f"\n✅ Analysis complete in {report.get('duration_seconds', 0):.2f} seconds")
    print(f"📄 Full report saved to JSON file")


if __name__ == "__main__":
    asyncio.run(main())
